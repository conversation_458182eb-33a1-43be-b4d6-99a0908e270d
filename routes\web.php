<?php

use App\Models\Page;
use App\Models\User;
use Illuminate\Http\Request;
use App\Livewire\Settings\Profile;
use App\Livewire\Settings\Password;
use Illuminate\Support\Facades\Auth;
use App\Livewire\Settings\Appearance;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ProductController;
use LBCDev\OAuthManager\Http\Controllers\OAuthController;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;


Route::post('/set-language', function (Request $request) {
    $locale = $request->input('locale');

    if (in_array($locale, array_keys(LaravelLocalization::getSupportedLocales()))) {
        session(['locale' => $locale]);
        app()->setLocale($locale);
    }

    $redirectUrl = LaravelLocalization::getLocalizedURL($locale, $request->input('redirect', '/'));

    return redirect($redirectUrl);
})->name('language.set');

Route::group([
    'prefix' => LaravelLocalization::setLocale(),
    'middleware' => ['localeSessionRedirect', 'localizationRedirect', 'localeViewPath']
], function () {
    Route::get('/', [HomeController::class, 'index'])->name('home');

    // Rutas para productos
    Route::get('/products', [ProductController::class, 'index'])->name('products.index');
    Route::get('/products/{product}', [ProductController::class, 'show'])->name('products.show');
});

// Route::view('dashboard', 'dashboard')
//     ->middleware(['auth', 'verified'])
//     ->name('dashboard');

// Dashboard con redirección por rol
Route::get('dashboard', function () {
    /** @var User */
    $user = Auth::user();

    // Si no hay usuario autenticado, redirigir al home
    if (!$user) {
        return redirect()->route('home');
    }

    // Si no tiene rol, redirigir al home
    if (!$user->hasRole('admin') && !$user->hasRole('cliente')) {
        return redirect()->route('home');
    }

    $redirectUrl = '/';

    // Determinar URL de redirección según el rol
    if ($user->hasRole('admin')) {
        $redirectUrl = '/admin';
    } elseif ($user->hasRole('cliente')) {
        $redirectUrl = '/app'; // Temporal - cambiar por /app cuando esté listo
    }

    // Crear una vista temporal para redirección con JavaScript
    return view('dashboard-redirect', compact('redirectUrl'));
})
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');
});

// Shopping Cart Routes
Route::prefix('cart')->name('cart.')->group(function () {
    Route::get('/', [App\Http\Controllers\CartController::class, 'index'])->name('index');
    Route::post('/add', [App\Http\Controllers\CartController::class, 'add'])->name('add');
    Route::put('/update', [App\Http\Controllers\CartController::class, 'update'])->name('update');
    Route::delete('/remove', [App\Http\Controllers\CartController::class, 'remove'])->name('remove');
    Route::delete('/clear', [App\Http\Controllers\CartController::class, 'clear'])->name('clear');
    Route::get('/count', [App\Http\Controllers\CartController::class, 'count'])->name('count');
    Route::get('/summary', [App\Http\Controllers\CartController::class, 'summary'])->name('summary');
    Route::get('/view', function () {
        return view('cart.index');
    })->name('view');
});

// Checkout Routes (require authentication)
Route::middleware(['auth'])->prefix('checkout')->name('checkout.')->group(function () {
    Route::get('/', [App\Http\Controllers\CheckoutController::class, 'index'])->name('index');
    Route::post('/process', [App\Http\Controllers\CheckoutController::class, 'process'])->name('process');
    Route::get('/success/{order}', [App\Http\Controllers\CheckoutController::class, 'success'])->name('success');
    Route::get('/cancel', [App\Http\Controllers\CheckoutController::class, 'cancel'])->name('cancel');
    Route::get('/summary', [App\Http\Controllers\CheckoutController::class, 'summary'])->name('summary');
});

// Payment Routes
Route::prefix('payment')->name('payment.')->group(function () {
    // Public routes for callbacks and webhooks
    Route::get('/success', [App\Http\Controllers\PaymentController::class, 'success'])->name('success');
    Route::get('/cancel', [App\Http\Controllers\PaymentController::class, 'cancel'])->name('cancel');
    Route::post('/notify', [App\Http\Controllers\PaymentController::class, 'notify'])->name('notify');
    Route::any('/webhook', [App\Http\Controllers\PaymentController::class, 'notify'])->name('webhook');

    // Authenticated routes
    Route::middleware(['auth'])->group(function () {
        Route::get('/status', [App\Http\Controllers\PaymentController::class, 'status'])->name('status');
    });
});

// Include auth routes before catch-all route
require __DIR__ . '/auth.php';

// Product and Order Pages
Route::get('/orders', function () {
    return view('orders.index');
})->name('orders.index');

// Home page with cart navigation
Route::get('/home-with-cart', function () {
    return view('home-with-cart');
})->name('home.with-cart');

// Catch-all route for pages (must be last)
Route::get('/{slug}', function ($slug) {
    $page = Page::where('slug', $slug)->where('is_published', true)->firstOrFail();
    return view('page', compact('page'));
});
