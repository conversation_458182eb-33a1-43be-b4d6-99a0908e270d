<?php

namespace App\Filament\Resources\OAuthServiceResource\Pages;

use App\Filament\Resources\OAuthServiceResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditOAuthService extends EditRecord
{
    protected static string $resource = OAuthServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('authorize')
                ->label('Authorize Service')
                ->icon('heroicon-m-key')
                ->color('success')
                ->visible(fn() => !$this->record->access_token || $this->record->isTokenExpired())
                ->url(fn() => route('oauth-manager.authorize', $this->record)),

            Actions\DeleteAction::make(),
        ];
    }
}
