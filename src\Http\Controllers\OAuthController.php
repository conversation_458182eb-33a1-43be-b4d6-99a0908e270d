<?php

namespace LBCDev\OAuthManager\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;

class OAuthController extends Controller
{
    public function authorize(OAuthService $service)
    {
        $provider = $service->getProviderInstance();
        $authUrl = $provider->getAuthorizationUrl();

        return redirect($authUrl);
    }

    public function callback(Request $request, OAuthService $service)
    {
        if ($request->has('error')) {
            return redirect()->back()->with('error', 'Authorization failed: ' . $request->get('error_description'));
        }

        $code = $request->get('code');
        if (!$code) {
            return redirect()->back()->with('error', 'Authorization code not received');
        }

        try {
            $provider = $service->getProviderInstance();
            $tokenData = $provider->handleCallback($code);

            $service->update([
                'access_token' => $tokenData['access_token'],
                'refresh_token' => $tokenData['refresh_token'],
                'expires_at' => $tokenData['expires_at'],
            ]);

            return redirect()->back()->with('success', 'Service authorized successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Authorization failed: ' . $e->getMessage());
        }
    }
}
