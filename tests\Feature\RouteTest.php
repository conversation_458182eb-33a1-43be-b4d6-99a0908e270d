<?php

namespace LBCDev\OAuthManager\Tests\Feature;

use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class RouteTest extends TestCase
{
    use RefreshDatabase;

    public function test_test_paquete_route_works()
    {
        $response = $this->get('/test-paquete');

        $response->assertStatus(200);
        $response->assertSeeText('Paquete funcionando');
    }

    public function test_authorize_route_exists()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Google Drive',
            'slug' => 'google-drive',
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ])->fresh();

        $this->assertIsArray($service->credentials);
        $this->assertIsArray($service->credentials);
        $this->assertArrayHasKey('client_id', $service->credentials);
        $this->assertArrayHasKey('client_secret', $service->credentials);

        $response = $this->get(route('oauth-manager.authorize', $service->slug));


        // Debería redirigir a la URL de autorización
        $response->assertStatus(302);
    }

    public function test_callback_route_exists()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Google Drive',
            'slug' => 'google-drive',
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        $response = $this->get(route('oauth-manager.callback', $service) . '?error=access_denied');

        $response->assertStatus(302);
    }
}
