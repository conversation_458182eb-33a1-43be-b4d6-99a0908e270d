# OAuth Manager

Un paquete <PERSON> completo para gestionar autenticación OAuth con múltiples servicios externos. Simplifica la integración con APIs como Google Drive, YouTube, Dropbox y más.

## Características

-  ✅ **Gestión automática de tokens** - Renovación automática de tokens expirados
-  ✅ **M<PERSON>tiples servicios** - Soporte para varios servicios OAuth del mismo tipo
-  ✅ **Seguridad integrada** - Encriptación opcional de credenciales sensibles
-  ✅ **Interfaz de administración** - Comandos Artisan para testing y gestión
-  ✅ **Extensible** - Fácil adición de nuevos proveedores OAuth
-  ✅ **Testing completo** - Suite de tests unitarios y de integración
-  ✅ **Configuración flexible** - Middleware y rutas personalizables

## Requisitos

-  PHP 8.2+
-  Laravel 12.0+
-  Base de datos compatible con JSON (MySQL 5.7+, PostgreSQL, SQLite)

## Instalación

### 1. Instalar el paquete

```bash
composer require lbcdev/oauth-manager
```

### 2. Publicar archivos de configuración

```bash
# Publicar solo la configuración
php artisan vendor:publish --tag=oauth-manager-config

# Publicar todo (configuración, migraciones, seeders)
php artisan vendor:publish --tag=oauth-manager
```

### 3. Ejecutar migraciones

```bash
php artisan migrate
```

## Configuración

### Configuración básica

El archivo `config/oauth-manager.php` contiene la configuración principal:

```php
return [
    'services' => [
        'google_drive' => [
            'name' => 'Google Drive',
            'icon' => 'heroicon-o-folder',
            'provider' => \LBCDev\OAuthManager\Providers\GoogleDriveProvider::class,
            'scopes' => [
                'https://www.googleapis.com/auth/drive.file',
                'https://www.googleapis.com/auth/drive.metadata.readonly'
            ],
            'fields' => [
                'client_id' => env('GOOGLE_DRIVE_CLIENT_ID'),
                'client_secret' => env('GOOGLE_DRIVE_CLIENT_SECRET'),
                'redirect_uri' => env('GOOGLE_DRIVE_REDIRECT_URI'),
            ]
        ],
    ],
    'callback_route' => 'oauth-manager.callback',
    'middleware' => ['web'],
];
```

### Variables de entorno

Añade a tu archivo `.env`:

```env
GOOGLE_DRIVE_CLIENT_ID=tu-client-id
GOOGLE_DRIVE_CLIENT_SECRET=tu-client-secret
GOOGLE_DRIVE_REDIRECT_URI=http://localhost/oauth-manager/callback/google_drive
```

## Configuración de Google OAuth

### 1. Crear proyecto en Google Cloud Console

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crear nuevo proyecto o seleccionar uno existente
3. Habilitar las APIs necesarias:
   -  Google Drive API
   -  Google OAuth 2.0 API

### 2. Configurar OAuth

1. **Pantalla de consentimiento OAuth**:

   -  Tipo: Externo
   -  Nombre de la aplicación
   -  Email de soporte
   -  Dominios autorizados

2. **Crear credenciales OAuth**:
   -  Tipo: Aplicación web
   -  URIs de redirección autorizados:
      -  Desarrollo: `http://localhost/oauth-manager/callback/google_drive`
      -  Producción: `https://tudominio.com/oauth-manager/callback/google_drive`

### 3. Configurar scopes

Los scopes más comunes para Google Drive:

```php
'scopes' => [
    'https://www.googleapis.com/auth/drive.file',           // Acceso a archivos creados por la app
    'https://www.googleapis.com/auth/drive.metadata.readonly', // Metadatos de solo lectura
    'https://www.googleapis.com/auth/drive',                   // Acceso completo a Drive
],
```

## Uso básico

### Crear un servicio OAuth

```php
use LBCDev\OAuthManager\Models\OAuthService;

$service = OAuthService::create([
    'name' => 'Mi cuenta de Google Drive',
    'service_type' => 'google_drive',
    'slug' => 'mi-google-drive',
    'credentials' => [
        'client_id' => 'tu-client-id',
        'client_secret' => 'tu-client-secret',
    ],
    'is_active' => true,
]);
```

### Autorizar un servicio

```php
// Redirigir al usuario para autorización
return redirect()->route('oauth-manager.authorize', ['service' => $service]);
```

### Obtener un token válido

```php
use LBCDev\OAuthManager\Services\OAuthManager;

$oauthManager = app(OAuthManager::class);

// Obtener token del primer servicio activo del tipo
$token = $oauthManager->getValidToken('google_drive');

// Obtener token de un servicio específico por nombre
$token = $oauthManager->getValidToken('google_drive', 'Mi cuenta de Google Drive');
```

### Usar el token con Google Drive

```php
use Google_Client;
use Google_Service_Drive;
use Google_Service_Drive_DriveFile;

$token = $oauthManager->getValidToken('google_drive');

if ($token) {
    $client = new Google_Client();
    $client->setAccessToken($token);

    $driveService = new Google_Service_Drive($client);

    // Subir archivo
    $file = new Google_Service_Drive_DriveFile();
    $file->setName('documento.pdf');

    $result = $driveService->files->create($file, [
        'data' => file_get_contents('path/to/documento.pdf'),
        'mimeType' => 'application/pdf',
    ]);
}
```

## Comandos Artisan

### Probar conexión OAuth

```bash
# Probar conexión de un servicio específico
php artisan oauth:test {service_id}

# Probar y refrescar token automáticamente si es necesario
php artisan oauth:test {service_id} --refresh
```

### Refrescar tokens

```bash
# Refrescar token de un servicio específico
php artisan oauth:refresh {service_id}

# Refrescar todos los tokens que lo necesiten
php artisan oauth:refresh --all
```

## API del OAuthManager

### Métodos principales

```php
use LBCDev\OAuthManager\Services\OAuthManager;

$manager = new OAuthManager();

// Obtener servicio por tipo
$service = $manager->getService('google_drive');

// Obtener servicio por tipo y nombre
$service = $manager->getService('google_drive', 'Mi cuenta');

// Refrescar token si es necesario
$success = $manager->refreshTokenIfNeeded($service);

// Obtener token válido (refresca automáticamente si es necesario)
$token = $manager->getValidToken('google_drive', 'Mi cuenta');
```

## Modelo OAuthService

### Propiedades principales

```php
$service = OAuthService::find(1);

$service->name;           // Nombre del servicio
$service->service_type;   // Tipo de servicio (google_drive, dropbox, etc.)
$service->slug;           // Slug único
$service->credentials;    // Array con credenciales
$service->access_token;   // Token de acceso actual
$service->refresh_token;  // Token de renovación
$service->expires_at;     // Fecha de expiración
$service->is_active;      // Si está activo
$service->last_used_at;   // Última vez usado
```

### Métodos útiles

```php
// Verificar si el token está expirado
$service->isTokenExpired();

// Verificar si necesita renovación
$service->needsRefresh();

// Obtener instancia del proveedor
$provider = $service->getProviderInstance();
```

## Extender con nuevos proveedores

### 1. Crear el proveedor

```php
<?php

namespace App\OAuth\Providers;

use LBCDev\OAuthManager\Providers\BaseOAuthProvider;

class CustomProvider extends BaseOAuthProvider
{
    public function getAuthorizationUrl(): string
    {
        // Implementar lógica de autorización
    }

    public function handleCallback(string $code): array
    {
        // Manejar callback y retornar tokens
        return [
            'access_token' => $token->getToken(),
            'refresh_token' => $token->getRefreshToken(),
            'expires_at' => $token->getExpires() ? now()->addSeconds($token->getExpires() - time()) : null,
        ];
    }

    public function refreshToken(): ?array
    {
        // Implementar renovación de token
    }

    public function revokeToken(): bool
    {
        // Implementar revocación de token
    }

    public function testConnection(): bool
    {
        // Implementar test de conexión
    }
}
```

### 2. Registrar en configuración

```php
// config/oauth-manager.php
'services' => [
    'custom_service' => [
        'name' => 'Custom Service',
        'icon' => 'heroicon-o-star',
        'provider' => App\OAuth\Providers\CustomProvider::class,
        'scopes' => ['read', 'write'],
        'fields' => [
            'client_id' => 'Client ID',
            'client_secret' => 'Client Secret',
        ]
    ],
],
```

## Seguridad

### Encriptación de tokens

Para habilitar encriptación de tokens sensibles, descomenta en el modelo:

```php
// src/Models/OAuthService.php
protected $casts = [
    'credentials' => 'array',
    'access_token' => Encrypted::class,    // Descomenta para encriptar
    'refresh_token' => Encrypted::class,   // Descomenta para encriptar
    'expires_at' => 'datetime',
    'last_used_at' => 'datetime',
    'is_active' => 'boolean',
];
```

### Recomendaciones de seguridad

-  Usa HTTPS en producción
-  Restringe credenciales OAuth por dominio/IP
-  Implementa rotación de secretos
-  Monitorea el uso de tokens
-  Revoca tokens no utilizados

## Testing

El paquete incluye una suite completa de tests:

```bash
# Ejecutar todos los tests
vendor/bin/phpunit

# Ejecutar tests específicos
vendor/bin/phpunit tests/Unit/OAuthServiceTest.php
vendor/bin/phpunit tests/Feature/TokenRefreshTest.php
```

### Ejemplo de test personalizado

```php
use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;

class CustomOAuthTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_get_valid_token()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'access_token' => 'valid_token',
            'expires_at' => now()->addHour(),
        ]);

        $manager = new OAuthManager();
        $token = $manager->getValidToken('google_drive');

        $this->assertEquals('valid_token', $token);
    }
}
```

## Troubleshooting

### Problemas comunes

**Error "redirect_uri_mismatch"**

-  Verifica que las URIs en la configuración OAuth coincidan exactamente
-  Asegúrate de incluir el protocolo (http/https)

**Token no se refresca automáticamente**

-  Verifica que `access_type=offline` esté configurado
-  Confirma que el `refresh_token` no sea nulo

**Permisos insuficientes**

-  Revisa los scopes solicitados
-  Verifica que el usuario haya otorgado permisos necesarios

**Errores de conexión**

-  Usa el comando `php artisan oauth:test` para diagnóstico
-  Verifica que las credenciales sean válidas

## Contribuir

1. Fork el repositorio
2. Crea una rama para tu feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit tus cambios (`git commit -am 'Añadir nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## Licencia

Este paquete está bajo la licencia MIT. Ver archivo `LICENSE` para más detalles.

## Autor

**Luis BC** - [<EMAIL>](mailto:<EMAIL>)

---

## Recursos adicionales

-  [Documentación de Google OAuth](https://developers.google.com/identity/protocols/oauth2)
-  [Laravel Package Development](https://laravel.com/docs/packages)
-  [OAuth 2.0 RFC](https://tools.ietf.org/html/rfc6749)
