<?php

namespace App\Models;

use App\Models\Language;
use Illuminate\Database\Eloquent\Model;
use LBCDev\OAuthManager\Models\OAuthService;

class File extends Model
{
    protected $fillable = [
        'nombre',
        'url',
        'oauth_service_id',
        'language_id',
        'orden',
    ];

    public function products()
    {
        return $this->belongsToMany(Product::class);
    }

    public function language()
    {
        return $this->belongsTo(Language::class);
    }

    public function oauthService()
    {
        return $this->belongsTo(OAuthService::class);
    }
}
